# IamMusicPlayer (IMP)

The Ikisugi Music Player MOD (Minecraft Forge MOD)

<img alt="GitHub" src="https://img.shields.io/github/license/teamfelnull/iammusicplayer?style=for-the-badge"> <img alt="Minecraft 1.19.2" src="https://img.shields.io/badge/Minecraft-1.19.2-green.svg?style=for-the-badge"><br> <img alt="CurseForge" src="https://cf.way2muchnoise.eu/versions/386380.svg">

# Contributor

``
fabric/src/main/generated
``  
``
forge/src/generated
``  
Do not edit these generated directories directly.  
If you want to change the contents, please change the IamMusicPlayerDataGenerator or change the resources folder.

## Transration

If a kind person translates it, please open an [ Issue or Pull requests ] !

[Templates used for translation](https://github.com/TeamFelnull/IamMusicPlayer/tree/master/common/src/main/resources/assets/iammusicplayer/lang)  
[Template used for translating patchouli](https://github.com/TeamFelnull/IamMusicPlayer/tree/master/resources/data/iammusicplayer/patchouli_books/manual)

# Using libraries

The following libraries are used for this mod,  
but they are included in the mod's jar file and do not need to be installed separately.

[LavaPlayer](https://github.com/sedmelluq/lavaplayer)  
[LavaPlayer (fork)](https://github.com/walkyst/lavaplayer-fork)   
[LavaPLayerNatives (macOS M1)](https://github.com/aikaterna/lavaplayer-natives)  
[Felnull Java library](https://github.com/TeamFelnull/FelNullJavaLibrary)  
[Java Youtube Downloader](https://github.com/sealedtx/java-youtube-downloader)  
[Mp3agic](https://github.com/mpatric/mp3agic)  
etc..

# Download

https://www.curseforge.com/minecraft/mc-mods/iammusicplayer
