plugins {
    id "com.github.johnrengelman.shadow" version "8.1.1"
}

architectury {
    platformSetupLoomIde()
    forge()
}

loom {
    accessWidenerPath = project(":common").loom.accessWidenerPath

    forge {
        convertAccessWideners = true
        extraAccessWideners.add loom.accessWidenerPath.get().asFile.name

        mixinConfig "iammusicplayer-common.mixins.json"

        dataGen {
            mod project.archives_base_name
        }
    }

    /* launches {
        data {
            arg "--existing", file("src/main/resources").absolutePath
        }
    }*/
}

configurations {
    common
    shadowCommon // Don't use shadow from the shadow plugin because we don't want IDEA to index this.
    compileClasspath.extendsFrom common
    runtimeClasspath.extendsFrom common
    developmentForge.extendsFrom common
    forgeDependencies.extendsFrom shadowIn
    shadowCommon.extendsFrom shadowIn
}

configurations.all {
    resolutionStrategy {
        force 'org.json:json:20220924'
        force 'org.jsoup:jsoup:1.15.3'
        force 'commons-io:commons-io:2.11.0'
    }
}

dependencies {
    forge "net.minecraftforge:forge:${rootProject.forge_version}"
    // Remove the next line if you don't want to depend on the API
    modApi "dev.architectury:architectury-forge:${rootProject.architectury_version}"

    common(project(path: ":common", configuration: "namedElements")) { transitive false }
    shadowCommon(project(path: ":common", configuration: "transformProductionForge")) { transitive = false }

    modApi "dev.felnull:otyacraftengine-forge:${rootProject.oe_version}"
    modApi "me.shedaniel.cloth:cloth-config-forge:${rootProject.cloth_config}"
    modApi "me.shedaniel:RoughlyEnoughItems-forge:${rootProject.rei}"
    modApi "curse.maven:ctm-535489:${rootProject.soundphysics_forge}"

    modCompileOnly "vazkii.patchouli:Patchouli:1.19.2-76:api"
    modRuntimeOnly "vazkii.patchouli:Patchouli:1.19.2-76"

    shadowIn("dev.arbjerg:lavaplayer:${rootProject.lava_natives}") {
        exclude group: 'dev.arbjerg', module: 'lavaplayer-natives'
    }

    shadowIn('com.fasterxml.jackson.core:jackson-core:2.14.3')
    shadowIn('com.fasterxml.jackson.core:jackson-databind:2.14.3')
    shadowIn "com.github.sealedtx:java-youtube-downloader:${rootProject.ytdownloader}"

    shadowIn("dev.lavalink.youtube:v2:${rootProject.youtube_src}")
    
    shadowIn "dev.felnull:felnull-java-library:${rootProject.felnull_version}"
    shadowIn 'com.mpatric:mp3agic:0.9.1'

    // Downgrade potentially problematic dependencies
    shadowIn('org.json:json:20220924') // Older version from 2022
    shadowIn('org.jsoup:jsoup:1.15.3') // Older version compatible with Java 17
    shadowIn('commons-io:commons-io:2.11.0') // Older version compatible with Java 17
}

processResources {
    inputs.property "version", project.version

    filesMatching("META-INF/mods.toml") {
        expand "version": project.version
    }
}
shadowJar {
    configurations = [project.configurations.shadowIn]
    relocate 'org.json', 'dev.felnull.imp.include.org.json'
    relocate 'org.slf4j', 'dev.felnull.imp.include.org.slf4j'
    relocate 'certificates', 'dev.felnull.imp.include.certificates'
    relocate 'com.fasterxml', 'dev.felnull.imp.include.com.fasterxml'
    relocate 'org.jsoup', 'dev.felnull.imp.include.org.jsoup'
    relocate 'natives', 'dev.felnull.imp.include.natives'
    relocate 'mozilla', 'dev.felnull.imp.include.mozilla'
    relocate 'net.iharder', 'dev.felnull.imp.include.net.iharder'
    relocate 'com.sedmelluq.lava', 'dev.felnull.imp.include.com.sedmelluq.lava'
    relocate 'org.apache.http', 'dev.felnull.imp.include.org.apache.http'
    relocate('org.apache.commons', 'dev.felnull.imp.include.org.apache.commons') {
        include 'org.apache.commons.logging.**'
        include 'org.apache.commons.io.**'
        include 'org.apache.commons.codec.**'
    }
    relocate 'com.github', 'dev.felnull.imp.include.com.github'
    relocate 'com.alibaba', 'dev.felnull.imp.include.com.alibaba'
    relocate 'dev.felnull.fnjl', 'dev.felnull.imp.include.dev.felnull.fnjl'
    relocate 'com.mpatric', 'dev.felnull.imp.include.com.mpatric'
    relocate('com.sedmelluq.discord.lavaplayer', 'dev.felnull.imp.include.com.sedmelluq.discord.lavaplayer') {
        exclude 'com.sedmelluq.discord.lavaplayer.natives.**'
    }

    mergeServiceFiles {
        relocate 'javax.ws.rs.ext', 'dev.felnull.imp.include.javax.ws.rs.ext'
        // relocate 'javax.script.ScriptEngineFactory', 'dev.felnull.imp.include.javax.script.ScriptEngineFactory'
        relocate 'org.glassfish.jersey.internal.spi', 'dev.felnull.imp.include.org.glassfish.jersey.internal.spi'
    }

    relocate 'ibxm', 'dev.felnull.imp.include.ibxm'
    relocate 'net.sourceforge.jaad.aac', 'dev.felnull.imp.include.net.sourceforge.jaad.aac'
    relocate 'org.mozilla.javascript', 'dev.felnull.imp.include.org.mozilla.javascript'
    relocate 'org.mozilla.classfile', 'dev.felnull.imp.include.org.mozilla.classfile'
}
shadowJar {
    exclude "fabric.mod.json"
    exclude "architectury.common.json"

    configurations = [project.configurations.shadowCommon]
    archiveClassifier = "dev-shadow"
}

remapJar {
    input.set shadowJar.archiveFile
    dependsOn shadowJar
    archiveClassifier = null
    setArchivesBaseName("${rootProject.archives_base_name}-${project.name}-${rootProject.mod_version}-mc${rootProject.minecraft_version}")
}

jar {
    archiveClassifier = "dev"
}

sourcesJar {
    def commonSources = project(":common").sourcesJar
    dependsOn commonSources
    from commonSources.archiveFile.map { zipTree(it) }
}

components.java {
    withVariantsFromConfiguration(project.configurations.shadowRuntimeElements) {
        skip()
    }
}
