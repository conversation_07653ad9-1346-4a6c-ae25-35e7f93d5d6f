# Changelog
Changelog to track updates for this mod.  
    Add your changes to Unreleased if you want to commit.  
    Please write according to [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)

## [Unreleased]

### Added

### Changed

### Deprecated

### Removed

### Fixed

### Security

## [3.19.12] - 2024-03-19

### Fixed
- Fixed the problem that YouTube cannot be played

## [3.19.11] - 2024-01-26

### Added
- Added config to hide display sprites and decorative antennas

## [3.19.10] - 2023-09-16

### Fixed
- Fixed an issue where YouTube could not be played (Fixed by updating LavaPlayer version)

## [3.19.9] - 2023-04-28

### Changed
- Updated LavaPlayer version to 1.4

### Fixed
- Fixed an issue where YouTube could not be played (Fixed by updating LavaPlayer version)

## [3.19.8] - 2023-03-27

### Fixed
- Fixed problems with recipes, etc. not being applied.

## [3.19.7] - 2023-03-27

### Added
- Add parabolic antenna craft recipe

## [3.19.6] - 2023-01-21

### Fixed
- Fixed a bug that could occur when reloading while playing music

## [3.19.5] - 2023-01-17

### Fixed
- Fixed server crash due to `NullPointerException` [Issues#45](https://github.com/TeamFelnull/IamMusicPlayer/issues/45)

[Unreleased]: https://github.com/TeamFelnull/IamMusicPlayer/compare/v3.19.12...HEAD
[3.19.12]: https://github.com/TeamFelnull/IamMusicPlayer/compare/v3.19.11...v3.19.12
[3.19.11]: https://github.com/TeamFelnull/IamMusicPlayer/compare/v3.19.10...v3.19.11
[3.19.10]: https://github.com/TeamFelnull/IamMusicPlayer/compare/v3.19.9...v3.19.10
[3.19.9]: https://github.com/TeamFelnull/IamMusicPlayer/compare/v3.19.8...v3.19.9
[3.19.8]: https://github.com/TeamFelnull/IamMusicPlayer/compare/v3.19.7...v3.19.8
[3.19.7]: https://github.com/TeamFelnull/IamMusicPlayer/compare/v3.19.6...v3.19.7
[3.19.6]: https://github.com/TeamFelnull/IamMusicPlayer/compare/v3.19.5...v3.19.6
[3.19.5]: https://github.com/TeamFelnull/IamMusicPlayer/commits/v3.19.5
