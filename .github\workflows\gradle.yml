name: Mod Build
on:
  - pull_request
  - push
  - workflow_dispatch
jobs:
  build:
    runs-on: blacksmith-4vcpu-ubuntu-2404
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 200
      - name: Set up JDK 17
        uses: useblacksmith/setup-java@v5
        with:
          java-version: 17
          distribution: zulu
          cache: gradle
      - name: Loom Cache
        uses: useblacksmith/cache@v5
        with:
          path: "**/.gradle/loom-cache"
          key: "${{ runner.os }}-gradle-${{ hashFiles('**/libs.versions.*', '**/*.gradle*', '**/gradle-wrapper.properties') }}"
          restore-keys: "${{ runner.os }}-gradle-"
      - uses: gradle/actions/wrapper-validation@v4
      - run: chmod +x ./gradlew
      - name: Run datagen and build
        run: ./gradlew :fabric:runDatagen build
        env:
          MAVEN_PASSWORD: ${{ secrets.MAVEN_PASSWORD }}
      - name: Upload Build Artifacts forge
        uses: actions/upload-artifact@v4
        with:
          name: IamMusicPlayerRenewedForge
          path: forge/build/libs
          compression-level: 9
      - name: Upload Build Artifacts fabric
        uses: actions/upload-artifact@v4
        with:
          name: IamMusicPlayerRenewedFabric
          path: fabric/build/libs
          compression-level: 9