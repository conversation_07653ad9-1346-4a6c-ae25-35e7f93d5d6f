plugins {
    id "com.github.johnrengelman.shadow" version "7.1.2"
}

architectury {
    platformSetupLoomIde()
    fabric()
}

sourceSets {
    main {
        resources {
            srcDirs += [
                    'src/main/generated'
            ]
        }
    }
}

loom {
    accessWidenerPath = project(":common").loom.accessWidenerPath

    runs {
        datagen {
            server()

            name "Minecraft Data"
            vmArg "-Dfabric-api.datagen"
            vmArg "-Dfabric-api.datagen.output-dir=${file("src/main/generated")}"

            runDir "run"
        }
    }
}
assemble.dependsOn runDatagen

configurations {
    common
    shadowCommon // Don't use shadow from the shadow plugin because we don't want IDEA to index this.
    compileClasspath.extendsFrom common
    runtimeClasspath.extendsFrom common
    developmentFabric.extendsFrom common
    implementation.extendsFrom shadowIn
    shadowCommon.extendsFrom shadowIn
}
dependencies {
    modImplementation "net.fabricmc:fabric-loader:${rootProject.fabric_loader_version}"
    modApi("net.fabricmc.fabric-api:fabric-api:${rootProject.fabric_api_version}")
    // Remove the next line if you don't want to depend on the API
    modApi "dev.architectury:architectury-fabric:${rootProject.architectury_version}"

    common(project(path: ":common", configuration: "namedElements")) { transitive false }
    shadowCommon(project(path: ":common", configuration: "transformProductionFabric")) { transitive false }

    modApi "dev.felnull:otyacraftengine-fabric:${rootProject.oe_version}"
    modApi "com.terraformersmc:modmenu:${rootProject.mod_menu}"
    modApi "me.shedaniel.cloth:cloth-config-fabric:${rootProject.cloth_config}"
    modApi "me.shedaniel:RoughlyEnoughItems-fabric:${rootProject.rei}"
    modApi "vazkii.patchouli:Patchouli:1.19.2-76-FABRIC"
    modApi "curse.maven:ctm-535489:${rootProject.soundphysics_fabric}"

    shadowIn("dev.arbjerg:lavaplayer:${rootProject.lava_natives}") {
        exclude group: 'dev.arbjerg', module: 'lavaplayer-natives'
        exclude group: 'com.fasterxml.jackson.core', module: 'jackson-core'
        exclude group: 'com.fasterxml.jackson.core', module: 'jackson-databind'
    }
    shadowIn('com.fasterxml.jackson.core:jackson-core:2.14.3')
    shadowIn('com.fasterxml.jackson.core:jackson-databind:2.14.3')
    shadowIn "com.github.sealedtx:java-youtube-downloader:${rootProject.ytdownloader}"

    shadowIn("dev.lavalink.youtube:v2:${rootProject.youtube_src}")
    
    shadowIn "dev.felnull:felnull-java-library:${rootProject.felnull_version}"
    shadowIn 'com.mpatric:mp3agic:0.9.1'

}

processResources {
    inputs.property "version", project.version

    filesMatching("fabric.mod.json") {
        expand "version": project.version
    }
}
shadowJar {
    configurations = [project.configurations.shadowIn]
    relocate 'org.json', 'dev.felnull.imp.include.org.json'
    relocate 'org.slf4j', 'dev.felnull.imp.include.org.slf4j'
    relocate 'certificates', 'dev.felnull.imp.include.certificates'
    relocate 'com.fasterxml', 'dev.felnull.imp.include.com.fasterxml'
    relocate 'org.jsoup', 'dev.felnull.imp.include.org.jsoup'
    relocate 'natives', 'dev.felnull.imp.include.natives'
    relocate 'mozilla', 'dev.felnull.imp.include.mozilla'
    relocate 'net.iharder', 'dev.felnull.imp.include.net.iharder'
    relocate 'com.sedmelluq.lava', 'dev.felnull.imp.include.com.sedmelluq.lava'
    relocate 'org.apache.http', 'dev.felnull.imp.include.org.apache.http'
    relocate('org.apache.commons', 'dev.felnull.imp.include.org.apache.commons') {
        include 'org.apache.commons.logging.**'
        include 'org.apache.commons.io.**'
        include 'org.apache.commons.codec.**'
    }
    relocate 'com.github', 'dev.felnull.imp.include.com.github'
    relocate 'com.alibaba', 'dev.felnull.imp.include.com.alibaba'
    relocate 'dev.felnull.fnjl', 'dev.felnull.imp.include.dev.felnull.fnjl'
    relocate 'com.mpatric', 'dev.felnull.imp.include.com.mpatric'
    relocate('com.sedmelluq.discord.lavaplayer', 'dev.felnull.imp.include.com.sedmelluq.discord.lavaplayer') {
        exclude 'com.sedmelluq.discord.lavaplayer.natives.**'
    }

    mergeServiceFiles {
        relocate 'javax.ws.rs.ext', 'dev.felnull.imp.include.javax.ws.rs.ext'
        //   relocate 'javax.script.ScriptEngineFactory', 'dev.felnull.imp.include.javax.script.ScriptEngineFactory'
        relocate 'org.glassfish.jersey.internal.spi', 'dev.felnull.imp.include.org.glassfish.jersey.internal.spi'
    }

    relocate 'ibxm', 'dev.felnull.imp.include.ibxm'
    relocate 'net.sourceforge.jaad.aac', 'dev.felnull.imp.include.net.sourceforge.jaad.aac'
    relocate 'org.mozilla.javascript', 'dev.felnull.imp.include.org.mozilla.javascript'
    relocate 'org.mozilla.classfile', 'dev.felnull.imp.include.org.mozilla.classfile'
}
shadowJar {
    exclude "architectury.common.json"

    configurations = [project.configurations.shadowCommon]
    archiveClassifier = "dev-shadow"
}

remapJar {
    injectAccessWidener = true
    input.set shadowJar.archiveFile
    dependsOn shadowJar
    archiveClassifier = null
    setArchivesBaseName("${rootProject.archives_base_name}-${project.name}-${rootProject.mod_version}-mc${rootProject.minecraft_version}")
}

jar {
    archiveClassifier = "dev"
}

sourcesJar {
    def commonSources = project(":common").sourcesJar
    dependsOn commonSources
    from commonSources.archiveFile.map { zipTree(it) }
}

components.java {
    withVariantsFromConfiguration(project.configurations.shadowRuntimeElements) {
        skip()
    }
}
