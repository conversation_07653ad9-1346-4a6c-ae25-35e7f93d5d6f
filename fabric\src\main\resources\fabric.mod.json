{"schemaVersion": 1, "id": "iammusicplayer", "version": "${version}", "name": "Iam Music Player Renewed", "description": "Add a music player that you can listen to with multiple players.\nThe ikisugi music player mod...", "authors": ["Mooo0042", "Shadowbee27"], "contributors": ["<PERSON>d<PERSON><PERSON><PERSON>"], "contact": {"email": "<EMAIL>", "homepage": "https://modrinth.com/mod/iam-music-player-renewed", "sources": "https://github.com/Mod-Sauce/IamMusicPlayer_FIX/", "issues": "https://github.com/Mod-Sauce/IamMusicPlayer_FIX/issues"}, "license": "GNU LGPLv3", "icon": "assets/iammusicplayer/icon.png", "environment": "*", "entrypoints": {"main": ["dev.felnull.imp.fabric.IamMusicPlayerFabric"], "client": ["dev.felnull.imp.fabric.client.IamMusicPlayerClientFabric"], "fabric-datagen": ["dev.felnull.imp.fabric.data.IamMusicPlayerDataGeneratorFabric"], "otyacraftengine_client": ["dev.felnull.imp.client.entrypoint.IMPOEClientEntryPoint"]}, "mixins": ["iammusicplayer-common.mixins.json"], "depends": {"fabric": "*", "minecraft": "=1.19.2", "architectury": ">=6.6.92", "cloth-config2": ">=8.3.134", "otyacraftengine": ">=3.2.0"}}