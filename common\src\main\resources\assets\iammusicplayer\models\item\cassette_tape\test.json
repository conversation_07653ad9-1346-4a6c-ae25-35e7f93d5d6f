{"credit": "Made with Blockbench", "textures": {"0": "iammusicplayer:item/cassette_tape/cassette_tape_base", "particle": "iammusicplayer:item/cassette_tape/cassette_tape_base"}, "elements": [{"from": [0, 0, 0], "to": [10, 0.25, 2.25], "faces": {"north": {"uv": [0, 0, 5, 0.25], "texture": "#0"}, "east": {"uv": [0, 0, 5, 0.25], "texture": "#0"}, "south": {"uv": [0, 0, 5, 0.25], "texture": "#0"}, "west": {"uv": [0, 0, 5, 0.25], "texture": "#0"}, "up": {"uv": [0, 0, 5, 1.25], "texture": "#0"}, "down": {"uv": [0, 1.75, 5, 3], "texture": "#0"}}}, {"from": [0, 0, 3.75], "to": [10, 0.25, 6], "faces": {"north": {"uv": [0, 0, 5, 0.25], "texture": "#0"}, "east": {"uv": [0, 0, 5, 0.25], "texture": "#0"}, "south": {"uv": [0, 0, 5, 0.25], "texture": "#0"}, "west": {"uv": [0, 0, 5, 0.25], "texture": "#0"}, "up": {"uv": [0, 1.75, 5, 3], "texture": "#0"}, "down": {"uv": [0, 0, 5, 1.25], "texture": "#0"}}}, {"from": [0, 0, 2.25], "to": [3, 0.25, 3.75], "faces": {"north": {"uv": [0, 0, 5, 0.25], "texture": "#0"}, "east": {"uv": [0, 0, 5, 0.25], "texture": "#0"}, "south": {"uv": [0, 0, 5, 0.25], "texture": "#0"}, "west": {"uv": [0, 0, 5, 0.25], "texture": "#0"}, "up": {"uv": [3.5, 1.25, 5, 1.75], "texture": "#0"}, "down": {"uv": [3.5, 1.25, 5, 1.75], "texture": "#0"}}}, {"from": [7, 0, 2.25], "to": [10, 0.25, 3.75], "faces": {"north": {"uv": [0, 0, 5, 0.25], "texture": "#0"}, "east": {"uv": [0, 0, 5, 0.25], "texture": "#0"}, "south": {"uv": [0, 0, 5, 0.25], "texture": "#0"}, "west": {"uv": [0, 0, 5, 0.25], "texture": "#0"}, "up": {"uv": [0, 1.25, 1.5, 1.75], "texture": "#0"}, "down": {"uv": [0, 1.25, 1.5, 1.75], "texture": "#0"}}}, {"from": [9.75, 0.25, 0], "to": [10, 0.75, 6], "faces": {"north": {"uv": [0, 3.25, 5, 3.75], "texture": "#0"}, "east": {"uv": [0, 3.25, 5, 3.75], "texture": "#0"}, "south": {"uv": [0, 3.25, 5, 3.75], "texture": "#0"}, "west": {"uv": [0, 3.25, 5, 3.75], "texture": "#0"}, "up": {"uv": [0, 3.25, 5, 3.75], "texture": "#0"}, "down": {"uv": [0, 3.25, 5, 3.75], "texture": "#0"}}}, {"from": [0, 0.25, 0], "to": [0.25, 0.75, 6], "faces": {"north": {"uv": [0, 3.25, 5, 3.75], "texture": "#0"}, "east": {"uv": [0, 3.25, 5, 3.75], "texture": "#0"}, "south": {"uv": [0, 3.25, 5, 3.75], "texture": "#0"}, "west": {"uv": [0, 3.25, 5, 3.75], "texture": "#0"}, "up": {"uv": [0, 3.25, 5, 3.75], "texture": "#0"}, "down": {"uv": [0, 3.25, 5, 3.75], "texture": "#0"}}}, {"from": [7.75, 0.25, 0], "to": [9.75, 0.75, 0.25], "faces": {"north": {"uv": [0, 3.25, 2, 3.75], "texture": "#0"}, "east": {"uv": [3, 3.25, 5, 3.75], "texture": "#0"}, "south": {"uv": [3, 3.25, 5, 3.75], "texture": "#0"}, "west": {"uv": [3, 3.25, 5, 3.75], "texture": "#0"}, "up": {"uv": [3, 3.25, 5, 3.75], "texture": "#0"}, "down": {"uv": [3, 3.25, 5, 3.75], "texture": "#0"}}}, {"from": [0.25, 0.25, 0], "to": [2.25, 0.75, 0.25], "faces": {"north": {"uv": [3, 3.25, 5, 3.75], "texture": "#0"}, "east": {"uv": [3, 3.25, 5, 3.75], "texture": "#0"}, "south": {"uv": [3, 3.25, 5, 3.75], "texture": "#0"}, "west": {"uv": [3, 3.25, 5, 3.75], "texture": "#0"}, "up": {"uv": [3, 3.25, 5, 3.75], "texture": "#0"}, "down": {"uv": [3, 3.25, 5, 3.75], "texture": "#0"}}}, {"from": [0.25, 0.25, 5.75], "to": [9.75, 0.75, 6], "faces": {"north": {"uv": [0, 3.25, 5, 3.75], "texture": "#0"}, "east": {"uv": [0, 3.25, 5, 3.75], "texture": "#0"}, "south": {"uv": [0, 3.25, 5, 3.75], "texture": "#0"}, "west": {"uv": [0, 3.25, 5, 3.75], "texture": "#0"}, "up": {"uv": [0, 3.25, 5, 3.75], "texture": "#0"}, "down": {"uv": [0, 3.25, 5, 3.75], "texture": "#0"}}}, {"from": [1.85, -0.75, 2.6], "to": [2.65, 1.5, 3.4], "color": 0, "faces": {"north": {"uv": [0, 0, 1, 2.25], "texture": "#missing"}, "east": {"uv": [0, 0, 1, 2.25], "texture": "#missing"}, "south": {"uv": [0, 0, 1, 2.25], "texture": "#missing"}, "west": {"uv": [0, 0, 1, 2.25], "texture": "#missing"}, "up": {"uv": [0, 0, 1, 1], "texture": "#missing"}, "down": {"uv": [0, 0, 1, 1], "texture": "#missing"}}}, {"from": [7.45, 0, 2.6], "to": [8.25, 1, 3.4], "color": 0, "faces": {"north": {"uv": [0, 0, 1, 2.25], "texture": "#missing"}, "east": {"uv": [0, 0, 1, 2.25], "texture": "#missing"}, "south": {"uv": [0, 0, 1, 2.25], "texture": "#missing"}, "west": {"uv": [0, 0, 1, 2.25], "texture": "#missing"}, "up": {"uv": [0, 0, 1, 1], "texture": "#missing"}, "down": {"uv": [0, 0, 1, 1], "texture": "#missing"}}}, {"from": [8.5, 0.25, 0.3], "to": [9, 0.75, 0.8], "color": 0, "faces": {"north": {"uv": [0, 0, 0.5, 0.5], "texture": "#missing"}, "east": {"uv": [0, 0, 0.5, 0.5], "texture": "#missing"}, "south": {"uv": [0, 0, 0.5, 0.5], "texture": "#missing"}, "west": {"uv": [0, 0, 0.5, 0.5], "texture": "#missing"}, "up": {"uv": [0, 0, 0.5, 0.5], "texture": "#missing"}, "down": {"uv": [0, 0, 0.5, 0.5], "texture": "#missing"}}}, {"from": [1, 0.25, 0.3], "to": [1.5, 0.75, 0.8], "color": 0, "faces": {"north": {"uv": [0, 0, 0.5, 0.5], "texture": "#missing"}, "east": {"uv": [0, 0, 0.5, 0.5], "texture": "#missing"}, "south": {"uv": [0, 0, 0.5, 0.5], "texture": "#missing"}, "west": {"uv": [0, 0, 0.5, 0.5], "texture": "#missing"}, "up": {"uv": [0, 0, 0.5, 0.5], "texture": "#missing"}, "down": {"uv": [0, 0, 0.5, 0.5], "texture": "#missing"}}}, {"from": [1.2, 0.25, 0.275], "to": [8.775, 0.75, 0.3], "color": 3, "faces": {"north": {"uv": [0, 0, 7.575, 0.5], "texture": "#missing"}, "east": {"uv": [0, 0, 0.025, 0.5], "texture": "#missing"}, "south": {"uv": [0, 0, 7.575, 0.5], "texture": "#missing"}, "west": {"uv": [0, 0, 0.025, 0.5], "texture": "#missing"}, "up": {"uv": [0, 0, 7.575, 0.025], "texture": "#missing"}, "down": {"uv": [0, 0, 7.575, 0.025], "texture": "#missing"}}}, {"from": [2, -0.25, 0], "to": [8, 0, 1.5], "faces": {"north": {"uv": [0, 3.25, 5, 3.75], "texture": "#0"}, "east": {"uv": [0, 3.25, 5, 3.75], "texture": "#0"}, "south": {"uv": [0, 3.25, 5, 3.75], "texture": "#0"}, "west": {"uv": [0, 3.25, 5, 3.75], "texture": "#0"}, "up": {"uv": [0, 3.25, 5, 3.75], "texture": "#0"}, "down": {"uv": [0, 3.25, 5, 3.75], "texture": "#0"}}}], "display": {"thirdperson_righthand": {"rotation": [-81, 0, 0], "translation": [1.5, 4.25, -2.5], "scale": [0.5, 0.5, 0.5]}, "thirdperson_lefthand": {"rotation": [-81, 0, 0], "translation": [-1.5, 4.25, -2.5], "scale": [0.5, 0.5, 0.5]}, "firstperson_righthand": {"rotation": [-141, 0, 0], "translation": [4, -0.5, -6.5]}, "firstperson_lefthand": {"rotation": [-141, 0, 0], "translation": [-2.5, -0.25, -6.5]}, "ground": {"translation": [1.75, 0, 2], "scale": [0.5, 0.5, 0.5]}, "gui": {"rotation": [-90, 0, 0], "translation": [3, 5, 0]}, "head": {"rotation": [-180, 0, 0], "translation": [2, -10.75, -11.5], "scale": [0.7, 0.7, 0.7]}, "fixed": {"rotation": [-90, 0, 0], "translation": [2.5, 3, -5.5], "scale": [0.7, 0.7, 0.7]}}}