architectury {
    common(rootProject.enabled_platforms.split(","))
}

loom {
    accessWidenerPath = file("src/main/resources/iammusicplayer.accesswidener")
}

dependencies {
    // We depend on fabric loader here to use the fabric @Environment annotations and get the mixin dependencies
    // Do NOT use other classes from fabric loader
    modImplementation "net.fabricmc:fabric-loader:${rootProject.fabric_loader_version}"
    // Remove the next line if you don't want to depend on the API
    modApi "dev.architectury:architectury:${rootProject.architectury_version}"

    modApi "dev.felnull:otyacraftengine:${rootProject.oe_version}"
    modApi "me.shedaniel.cloth:cloth-config:${rootProject.cloth_config}"

    implementation "dev.felnull:felnull-java-library:${rootProject.felnull_version}"

    implementation("dev.arbjerg:lavaplayer:${rootProject.lava_natives}") {
        exclude group: 'dev.arbjerg', module: 'lavaplayer-natives'
        exclude group: 'com.fasterxml.jackson.core', module: 'jackson-core'
        exclude group: 'com.fasterxml.jackson.core', module: 'jackson-databind'
    }
    implementation "com.fasterxml.jackson.core:jackson-core:2.14.3"
    implementation "com.fasterxml.jackson.core:jackson-databind:2.14.3"
    implementation "com.github.sealedtx:java-youtube-downloader:${rootProject.ytdownloader}"

    implementation "dev.lavalink.youtube:v2:${rootProject.youtube_src}"

    implementation 'com.mpatric:mp3agic:0.9.1'
}